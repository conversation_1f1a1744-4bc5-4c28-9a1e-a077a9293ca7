{% extends 'base.html' %}

{% block title %}Item Lookup - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Item Lookup</h1>
    <a href="{% url 'inventory:add_item' %}" class="btn btn-primary">Add New Item</a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Scan or Enter Item Code</h5>
            </div>
            <div class="card-body">
                <form method="post" id="lookup-form">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="item_code" class="form-label">Item Code</label>
                        <input type="text" class="form-control form-control-lg" id="item_code" name="item_code" 
                               placeholder="Enter 6-character code" maxlength="6" autofocus autocomplete="off">
                        <div class="form-text">
                            Enter a 6-character code (0-9, A-F). If the code exists, you'll be taken to that item.
                            If the code doesn't exist, you'll be prompted to create a new item with this code.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rfid_epc" class="form-label">RFID EPC</label>
                        <input type="text" class="form-control form-control-lg" id="rfid_epc" name="rfid_epc" 
                               placeholder="Enter 24-32 character EPC" maxlength="32" autocomplete="off">
                        <div class="form-text">
                            Alternatively, enter a 24-32 character RFID Electronic Product Code (0-9, A-F).
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">Look Up Item</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title">Quick Help</h5>
            </div>
            <div class="card-body">
                <h6>How to use Item Lookup:</h6>
                <ol>
                    <li>Scan a barcode/QR code with a scanner, or manually enter a 6-character code.</li>
                    <li>Press Enter or click "Look Up Item".</li>
                    <li>If the item exists in the system, you'll be taken to its details page.</li>
                    <li>If the item doesn't exist, you'll be prompted to create a new item with that code.</li>
                </ol>
                <p class="mb-0">
                    <a href="{% url 'inventory:generate_labels' %}" class="text-decoration-none">
                        Need to generate new labels? Click here &rarr;
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const itemCodeInput = document.getElementById('item_code');
        const rfidEpcInput = document.getElementById('rfid_epc');
        const form = document.getElementById('lookup-form');
        
        // Convert to uppercase for item code
        itemCodeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
        
        // Convert to uppercase for RFID EPC
        rfidEpcInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
        
        // Auto-submit when 6 characters are entered for item code
        itemCodeInput.addEventListener('input', function() {
            if(this.value.length === 6) {
                setTimeout(() => {
                    form.submit();
                }, 100); // Small delay to allow for scanner to finish input
            }
        });
        
        // Auto-submit when valid EPC is entered (24-32 chars)
        rfidEpcInput.addEventListener('input', function() {
            if(this.value.length >= 24 && this.value.length <= 32) {
                setTimeout(() => {
                    form.submit();
                }, 100); // Small delay to allow for scanner to finish input
            }
        });
    });
</script>
{% endblock %}
