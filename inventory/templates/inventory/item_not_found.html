{% extends 'base.html' %}

{% block title %}Item Not Found - Inventory Management{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Item Not Found</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    {% if item_code %}
                        <p>No item found with code <strong>{{ item_code }}</strong>.</p>
                    {% elif rfid_epc %}
                        <p>No item found with RFID EPC <strong>{{ rfid_epc }}</strong>.</p>
                    {% else %}
                        <p>No item found with the provided identifier.</p>
                    {% endif %}
                </div>
                
                <p>If you believe this item should exist, you may need to log in to access it or create it.</p>
                
                <div class="mt-4">
                    {% if user.is_authenticated %}
                        {% if item_code %}
                            <a href="{% url 'inventory:add_item_with_code' item_code=item_code %}" class="btn btn-primary">
                                Create Item with Code {{ item_code }}
                            </a>
                        {% elif rfid_epc %}
                            <a href="{% url 'inventory:add_item' %}?rfid_epc={{ rfid_epc }}" class="btn btn-primary">
                                Create Item with RFID EPC {{ rfid_epc }}
                            </a>
                        {% endif %}
                    {% else %}
                        <a href="{% url 'login' %}?next={{ request.path }}" class="btn btn-primary">
                            Log In
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
