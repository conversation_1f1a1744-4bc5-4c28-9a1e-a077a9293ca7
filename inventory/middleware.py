from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)

class OrganizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Skip for static and media URLs
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return self.get_response(request)
            
        # Skip for admin URLs
        if request.path.startswith('/admin/'):
            return self.get_response(request)
            
        # Skip for authentication URLs
        if request.path.startswith('/accounts/'):
            return self.get_response(request)
            
        # Skip for debug URLs
        if request.path.startswith('/debug-session/'):
            return self.get_response(request)
            
        # Get org_id from session
        org_id = request.session.get('org_id')
        
        # Store org_id in request for easy access in views
        request.org_id = org_id
        
        # If user is authenticated but no organization is selected
        if request.user.is_authenticated and not org_id:
            # Superusers can bypass organization selection
            if request.user.is_superuser and not request.path == reverse('inventory:select_organization'):
                # Log that we're allowing a superuser to bypass
                #logger.debug(f"Allowing superuser {request.user.username} to bypass organization selection")
                
                # If they're trying to access the item list, redirect to org selection
                if request.path == reverse('inventory:item_list'):
                    return redirect('inventory:select_organization')
                
                # Otherwise, allow them to proceed
                return self.get_response(request)
            
            # Get available organizations for this user
            from .models import Organization, OrganizationUser
            
            # Get organizations where user is a member
            user_orgs = Organization.objects.filter(
                organizationuser__user=request.user,
                is_active=True
            )
            
            if user_orgs.count() == 1:
                # If only one organization exists, select it automatically
                request.session['org_id'] = str(user_orgs.first().id)
                # logger.debug(f"Auto-selected organization {user_orgs.first().name} for user {request.user.username}")
            elif user_orgs.exists() and not request.path == reverse('inventory:select_organization'):
                # If multiple orgs exist but none selected, redirect to selection page
                # logger.debug(f"Redirecting user {request.user.username} to organization selection")
                messages.info(request, "Please select an organization to continue")
                return redirect('inventory:select_organization')
            elif not user_orgs.exists() and not request.path == reverse('inventory:no_organizations'):
                # If user has no organizations, redirect to a page explaining this
                # logger.debug(f"User {request.user.username} has no organizations, redirecting to no_organizations")
                return redirect('inventory:no_organizations')
        
        # Check if user has access to the selected organization
        if request.user.is_authenticated and org_id and not request.path.startswith('/accounts/'):
            from .models import OrganizationUser
            
            # Superusers have access to all organizations
            if request.user.is_superuser:
                logger.debug(f"Allowing superuser {request.user.username} access to organization {org_id}")
                return self.get_response(request)
            
            # Check if user is a member of the selected organization
            has_access = OrganizationUser.objects.filter(
                user=request.user,
                organization_id=org_id
            ).exists()
            
            if not has_access:
                # If user doesn't have access to this organization, clear the selection
                logger.warning(f"User {request.user.username} doesn't have access to organization {org_id}")
                del request.session['org_id']
                messages.warning(request, "You don't have access to that organization")
                return redirect('inventory:select_organization')
        
        response = self.get_response(request)
        return response
